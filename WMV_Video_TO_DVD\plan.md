# WMV Video TO DVD 文章创作执行计划

## 用户具体需求和目标
- **文章主题**: WMV Video TO DVD
- **SEO关键词**: WMV Video TO DVD, BURN WMV Video TO DVD, CONVERT WMV Video TO DVD
- **文章长度**: 1600字（不能少，最多可超出20%，即最多1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者
- **开头策略**: C（"What if"场景开头）
- **推荐产品**: Wondershare DVD Creator

## 需要执行的所有步骤详细清单

### 步骤1: 基础研究和竞品分析
- 分析参考URL内容结构
- 提取H2-H4级标题
- 识别内容空白和改进机会
- 收集SEO长尾关键词

### 步骤2: 生成超级大纲
- 合并整理提取的标题
- 按层级结构重新组织
- 保存为 `super_outline.md`

### 步骤3: 创建最终大纲
- 优化大纲结构
- 添加字数分配
- 整合SEO关键词
- 保存为 `final_outline.md`

### 步骤4: 撰写初稿
- 遵循人性化写作指南
- 严格控制各章节字数
- 整合产品推荐
- 添加内外部链接
- 保存为 `first_draft.md`

### 步骤5: 生成SEO内容
- 创建5组SEO标题和元描述
- 生成特色图片提示词
- 保存为 `seo_metadata_images.md`

### 步骤6: 最终检查
- 验证字数控制
- 检查AI语言痕迹
- 确认链接数量达标

## 每个步骤的完成标准和检查点

### 超级大纲完成标准
- [ ] 提取所有参考URL的标题结构
- [ ] 合并相似标题并标记源数量
- [ ] 按H1-H4层级重新组织

### 最终大纲完成标准
- [ ] 包含至少3个竞品未涵盖的独特观点
- [ ] 为每个H2章节准备人工经验要素
- [ ] 字数分配总和在1600-1920字范围内
- [ ] 包含SEO长尾关键词列表

### 初稿完成标准
- [ ] 严格遵循各章节字数限制
- [ ] 体现明显的人工成分和个人经验
- [ ] 包含3-5个内部链接
- [ ] 包含适量外部链接
- [ ] 为每个H2章节添加相关图片

### SEO内容完成标准
- [ ] 5组标题长度50-60字符
- [ ] 5组元描述长度120-150字符
- [ ] 1条特色图片提示词

## 预期输出文件清单
1. `plan.md` - 执行计划文件
2. `super_outline.md` - 初级大纲
3. `final_outline.md` - 最终大纲
4. `first_draft.md` - 文章初稿
5. `seo_metadata_images.md` - SEO内容和图片提示词
