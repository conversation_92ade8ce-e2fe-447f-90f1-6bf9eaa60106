# 第2部分：文章起草和完善
根据大纲生成初稿，并经过优化形成最终可以发表的文章版本。

## 步骤4：生成文章初稿

**生成章节草稿要求：**  
重要：初稿撰写是根据最终大纲`/{keyword}/final_outline.md`并遵循`New_article/hl.md`的拟人化写作的指令来为大纲中的每个章节创建真实、吸引人的内容，模拟自然人类写作模式，不让读者感觉是AI/机器语言。

**副标题（H2/H3/H4）**  
- 副标题要像真实博主写的一样，读者一眼扫过去就想点进去读。拒绝模板化、教科书化、流水账式的写法。每一个小标题都应该像是“下一段内容值得一读”的邀请。

**字数要求**（必须遵循）
- 重要：在撰写文章正文时，必须严格**遵循 `/final_outline.md` 中为每个章节设定的字数要求**和用户要求的文章总字数。超过用户规定的字数一定要修改，否则视为任务失败。

### 执行要求：
- 打开并读取 `/final_outline.md`，提取各章节的字数限制（可标注为字数区间或最大字数）。
- 在撰写每个章节内容时，**务必控制输出字数在对应限制范围内**。
- 若未提供具体字数，仅写明“简短”或“详细”，请根据上下文合理判断（例如：简短约为 100–150 字，详细为 300–500 字）。
- 严禁超出各章节字数限制，**不要在任何章节中“多写一点”**。
- 若某章节内容不足，请优化内容表达方式而非增加篇幅。

## 添加SEO长尾关键字
- 为了让文章在搜索引擎获取更多的关键字排名，在整个标题和内容中自然整合`/{keyword}/final_outline.md`中的**SEO NLP和长尾关键词**，同时保持文章整体的可读性。不要强制塞入关键字破坏句子流畅性。

## Google E-E-A-T标准
为了提升文章在Google眼中的质量，Implement Google's E-E-A-T (Experience, Expertise, Authoritativeness, and Trustworthiness) principles in article writing：
### ✅ Experience 展示经验（第一手洞察，真实案例）
- **指令核心：** 指导AI使用能够反映第一手经验、实际应用或个人测试的措辞和例证。
- **AI执行任务：** 生成假设但合理的情景、案例研究或轶事，以模拟第一手经验。  
  示例指令：  
  “在关于‘[特定子主题]’的章节中，生成一个简短但合理可信的轶事或微型案例研究（100-150字），用以阐释‘[概念/解决方案]’在真实世界场景中的运作方式。”
### ✅ Expertise 彰显专业性（作者资质，深度知识，专业术语）
- **指令核心：** 在内容中恰当体现深度专业知识，并在必要时建议提及作者资质或引用专家观点。
- **AI执行任务：**  
  示例指令：  
  “基于这篇关于‘[主题]’的文章大纲，找出2-3个关键章节，在这些章节中明确提及作者的专业知识（例如，‘凭借在X领域超过10年的经验……’）或引用领域内公认专家的观点。”
### ✅ Authoritativeness 构建权威性（引用数据，可靠来源，链接权威机构）
- **指令核心：** 识别内容中需要数据、统计或研究支持的论点，并建议引用或链接到权威外部资源。
- **AI执行任务：**  
  若具备浏览功能：  
  “从权威来源（例如.gov、.edu、主要研究机构）查找2-3个近期的统计数据或研究发现来支持‘[主题]’章节。”  
  若不具备浏览功能：  
  “建议哪些类型的数据或研究能够加强章节X中的论点，并指出可能找到此类数据的可靠途径。”
### ✅ Trustworthiness 保证可信度（透明度，准确性，清晰溯源）
- **指令核心：** 在内容中加入能提升透明度和准确性的元素，如方法论说明、免责声明或信息更新时间。
- **AI执行任务：**  
  示例指令：  
  “如果本文在章节[X]中呈现了原创数据或独特方法论，请加入简短声明说明数据来源。对于YMYL主题，加入免责声明说明内容仅供教育参考。”

### 🎯 信息增量实施要求
- **独特观点整合**: 在每个H2章节中至少包含1个其他文章未提及的独特见解
- **个人经验叙述**: 使用第一人称分享具体的使用经验、试错过程和解决方案
- **专业判断展示**: 明确表达作者的专业观点和推荐理由

### ✅ 人工成分强化指令
为确保内容体现明显的人工努力(Effort)：

1. **经验性叙述**：
   - 每个主要章节包含至少一个"我在使用中发现..."的个人经验
   - 分享具体的试错过程："起初我以为...，但实际测试后发现..."
   - 提供基于经验的具体建议："根据我的使用经验，建议..."

2. **主观判断表达**：
   - 使用"我认为"、"在我看来"、"我的建议是"等主观表达
   - 对比不同方案时给出明确的个人偏好和理由
   - 分享"意外发现"或"令人惊喜的功能"

3. **准确性保证**：
   - 所有技术信息必须基于实际测试或可靠来源
   - 避免模糊表述，提供具体的操作步骤和预期结果
   - 对可能的风险或限制进行诚实说明

### 🔍 搜索意图深度满足
- **长尾关键词覆盖**: 自然整合复杂查询词和相关问题
- **用户旅程考虑**: 从初学者到进阶用户的完整需求覆盖
- **问题解决导向**: 每个章节都要解决用户的具体问题或疑虑

## 产品推荐（重要）
- 推荐产品使用步骤必和产品图片素材已经在`New_article\info_aia.md`中说明，务必按照说明获取所有的推荐产品信息。
- 每次介绍完推荐产品的时候，**尽早提供下载链接和按钮**。
- **Windows 和 Mac 两个版本必须同时提供**，并**在同一排显示**：Windows 在前，Mac 在后。

## 为H2章节添加图片
- 在每个主要 H2 小节下方插入一张与该节内容密切相关的图片。图片应具备解释性或展示性，优先选择官方截图、功能示意图等，以帮助读者更直观理解内容。
- 图片获取方式：通过 Google 图片搜索，查找与小节内容相关的高质量图片。
- 禁止采用不能表达段落的大意的图片。
- Introduction，FAQ，Troubshooting，Conclusion 不需要添加图片。

## 添加内部链接
- **指令核心：** 在文章使用内部链接，尤其是在内容涉及多个章节或页面时。
- **AI执行任务：**  
  - 内链来源：所有链接必须来自网站地图：https://www.cinchsolution.com/sitemap/  
  - **禁止编造链接地址**  
  - 内链规则：
    - 在添加任何内部链接前，先查看最新的网站地图
    - 自动识别与概念相关的页面；
    - 用关键词作为锚文本；
    - 每个关键词仅链接一次，每个链接在同一篇文章中仅出现一次；
    - 插入方式自然流畅；
    - 每个文章添加3-5内链
    - 示例格式：  
      `...you can learn more in our [detailed Spotify Car Thing review](https://www.cinchsolution.com/spotify-car-thing-review/).`

## 添加外部链接
为产品、服务，数据，第三方工具和术语添加官方外链：
1. 每个外链仅出现一次；
2. 同一个关键字内链优先；
3. 使用具体描述作为锚文本；
4. 必须添加链接的内容：
   - 产品/服务名称
   - 技术术语
   - 第三方软件
   - 专用硬件
5. 每1000字包含3-5个外链链接；

## 丰富写作内容元素
- **指令核心：** 主动识别最佳信息展示方式，优先考虑用户决策需求。
- **AI执行任务：**  
  - 必须使用至少5种以下内容元素；
  - 必须主动判断是否需要表格、列表、图示等可视化内容。

### ✅ 应用场景
| 场景 | 内容元素 |
|------|-----------|
| 有3个以上产品/工具对比 | 创建比较表格 |
| 有多个步骤流程 | 使用数字列表或流程图 |
| 有重要警告 | 使用突出显示框 |
| 技术规格对比 | 使用表格展示 |
| 价格/功能对比 | 表格或图表展示 |

### ✅ 可用内容元素清单
- 比较表格（产品、功能、价格）
- 关键要点/警告的突出显示框
- 快速提示
- 来自专家的引用
- 视觉辅助图解
- 成功案例展示
- 风险警示说明
- 互动元素（如评分）
- 星级评价
- 流程图 / 时间线

完成后自我检查是否安装上述步骤完整执行，并将初稿以 `.md` 格式保存为`/{keyword}/first_draft.md`