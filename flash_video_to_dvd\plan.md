# Execution Plan for "Flash Video TO DVD"

## 1) User Requirements & Goals
- Topic: Flash Video TO DVD
- Language: English
- Length: 1600 words (can exceed by up to 20%)
- Timeframe: Use latest info as of Jun 2025
- Audience: Music lovers/creators interested in downloading, editing, sharing audio; however, this piece focuses on FLV (Flash Video) to DVD burning while keeping tone/music-audience friendly
- SEO Keywords: Flash Video TO DVD, BURN Flash Video TO DVD, CONVERT Flash Video TO DVD
- Recommended Product: Wondershare DVD Creator (all capabilities must match official page)
- Intro opening strategy: B (Rhetorical Question)
- Must include: personal trial-and-error stories, unique insights (3–5), actionable solutions, internal links (3–5 from site map), external links (3–5 per 1000 words), image under each H2 (not intro/FAQ/Troubleshooting/Conclusion), E-E-A-T, humanized writing from New_article/hl.md

## 2) Step-by-Step Checklist
1. Extract H2–H4 from reference URLs and compile super outline
2. Merge/simplify headings; rebuild hierarchy; exclude Intro/Conclusion; save as super_outline.md
3. Create final outline: integrate info gain research, unique angles, personal experience hooks; include SEO NLP + long-tail list at the end; assign target word counts per H2/H3 using rules in outline.md; ensure total ≤ 95% of max allowed
4. Draft article strictly following hl.md tone and word caps from final outline; integrate internal/external links; add images per H2; add product placement following car_guide.md rules; Windows/Mac download buttons on the same row
5. Generate SEO titles (5) + meta descriptions and 1 featured image prompt; validate character counts
6. Final QA: word count check; link validation (no 404); E-E-A-T and information gain audit

## 3) Completion Criteria & Checkpoints
- Super outline saved at /flash_video_to_dvd/super_outline.md
- Final outline saved at /flash_video_to_dvd/final_outline.md with word targets annotated
- First draft saved at /flash_video_to_dvd/first_draft.md meeting total word count target
- SEO metadata + featured image prompt saved at /flash_video_to_dvd/seo_metadata_images.md
- Links: 3–5 internal (from sitemap), 3–5 external per 1000 words; images under each H2 (relevant)

## 4) Expected Outputs
- super_outline.md
- final_outline.md
- first_draft.md
- seo_metadata_images.md
